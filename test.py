
#!/usr/bin/env python3
import os
import sys
import json
import yaml
import httpx
import argparse
import signal
import threading
from pathlib import Path
from typing import List, Dict, Optional
from rich.console import Console
from rich.table import Table
from rich.prompt import Prompt
from rich.live import Live
from rich.text import Text
from prompt_toolkit import PromptSession
from prompt_toolkit.key_binding import KeyBindings

CONFIG_PATH = Path.home() / ".ollama_turbo_cli.yaml"
OLLAMA_URL = "http://127.0.0.1:11434"

console = Console()
session = PromptSession()

# ----------------------------------------------------------------------
# Config management
# ----------------------------------------------------------------------
def save_config(cfg):
    with open(CONFIG_PATH, "w") as f:
        yaml.safe_dump(cfg, f)

def load_config():
    if CONFIG_PATH.exists():
        with open(CONFIG_PATH, "r") as f:
            return yaml.safe_load(f) or {}
    return {}

# ----------------------------------------------------------------------
# Ollama API helpers
# ----------------------------------------------------------------------
def ollama_list_models() -> List[Dict]:
    try:
        r = httpx.get(f"{OLLAMA_URL}/api/tags", timeout=5)
        r.raise_for_status()
        return r.json().get("models", [])
    except Exception as e:
        console.print(f"[red]Failed to fetch models from Ollama: {e}[/red]")
        return []

def ollama_chat(model: str, messages: List[Dict], images: Optional[List[str]] = None):
    """Stream chat responses from local Ollama."""
    payload = {"model": model, "messages": messages, "stream": True}
    if images:
        payload["images"] = images
    with httpx.stream("POST", f"{OLLAMA_URL}/api/chat", json=payload, timeout=None) as r:
        for line in r.iter_lines():
            if not line:
                continue
            try:
                data = json.loads(line)
                if "message" in data and "content" in data["message"]:
                    yield data["message"]["content"]
            except:
                continue

# ----------------------------------------------------------------------
# Turbo API placeholder
# ----------------------------------------------------------------------
def turbo_chat(token: str, messages: List[Dict]):
    # You would replace this with actual Turbo API call
    # For now, simulate with a dummy string
    yield "[Turbo API placeholder output — replace with real API call]"

# ----------------------------------------------------------------------
# CLI Class
# ----------------------------------------------------------------------
class ChatCLI:
    def __init__(self):
        self.cfg = load_config()
        self.messages = []
        self.model = self.cfg.get("model", None)
        self.turbo_token = self.cfg.get("turbo_token")
        self.use_turbo = self.cfg.get("use_turbo", False)
        self.kb = KeyBindings()
        self._bind_shortcuts()
        self.images_to_send = []

    def init_wizard(self):
        console.rule("[bold cyan]Ollama Turbo Chat CLI — Initialization")
        # Auto detect Ollama
        console.print(f"Checking Ollama at {OLLAMA_URL} …")
        models = ollama_list_models()
        if models:
            console.print("[green]✔ Ollama detected and running[/green]")
            # pick smallest model
            smallest = sorted(models, key=lambda m: m.get("size", 0))[0]
            self.model = smallest["name"]
            console.print(f"[cyan]Default model set to smallest available:[/cyan] {self.model}")
        else:
            console.print("[red]Could not detect Ollama or no models installed[/red]")

        # Turbo token
        t = Prompt.ask("Ollama Turbo token (press Enter to skip)", default="")
        if t.strip():
            self.turbo_token = t.strip()
            self.use_turbo = True
            console.print("[green]Turbo configured[/green]")
        else:
            console.print("Turbo not configured")

        # MCP key
        s = Prompt.ask("Smithery.ai API key (press Enter to skip)", default="")
        if s.strip():
            self.cfg["smithery_key"] = s.strip()
            console.print("[green]MCP configured[/green]")
        else:
            console.print("MCP not configured")

        self.cfg["model"] = self.model
        self.cfg["turbo_token"] = self.turbo_token
        self.cfg["use_turbo"] = self.use_turbo
        save_config(self.cfg)
        console.print("[bold green]Configuration saved successfully![/bold green]")

    def _bind_shortcuts(self):
        @self.kb.add("c-i")
        def _(event):
            event.app.current_buffer.insert_text("/image ")

        @self.kb.add("c-t")
        def _(event):
            self.use_turbo = not self.use_turbo
            mode = "Turbo" if self.use_turbo else "Local"
            console.print(f"[yellow]Switched to {mode} mode[/yellow]")

        @self.kb.add("c-b")
        def _(event):
            event.app.current_buffer.insert_text("/web ")

        @self.kb.add("c-m")
        def _(_):
            # Directly trigger MCP tool call instead of inserting text
            self.run_mcp_tool()

        @self.kb.add("c-l")
        def _(event):
            event.app.current_buffer.insert_text("/models")

    def show_models(self):
        models = ollama_list_models()
        if not models:
            return
        table = Table(title="Local Ollama Models")
        table.add_column("#", justify="right")
        table.add_column("Name")
        table.add_column("Size (MB)", justify="right")
        for idx, m in enumerate(models, start=1):
            table.add_row(str(idx), m["name"], str(round(m.get("size", 0) / (1024*1024), 2)))
        console.print(table)

    def pick_model(self, num: int):
        models = ollama_list_models()
        if 1 <= num <= len(models):
            self.model = models[num - 1]["name"]
            self.cfg["model"] = self.model
            save_config(self.cfg)
            console.print(f"[green]Switched to model:[/green] {self.model}")
        else:
            console.print("[red]Invalid model number[/red]")

    def run_mcp_tool(self, command: str = None):
        """Run MCP tool with optional command, or prompt for command if none provided."""
        if not self.cfg.get("smithery_key"):
            console.print("[red]MCP not configured. Run with --init to set up Smithery.ai API key[/red]")
            return

        if not command:
            try:
                command = session.prompt("MCP command: ")
                if not command.strip():
                    console.print("[yellow]No command entered[/yellow]")
                    return
            except (EOFError, KeyboardInterrupt):
                console.print("[yellow]MCP command cancelled[/yellow]")
                return

        # TODO: Implement actual MCP API call here
        # For now, just show what would be called
        console.print(f"[cyan]MCP Tool Call:[/cyan] {command}")
        console.print("[yellow]MCP integration not yet implemented - placeholder response[/yellow]")

        # You would replace this with actual MCP API call using self.cfg["smithery_key"]
        # Example structure:
        # try:
        #     response = call_mcp_api(self.cfg["smithery_key"], command)
        #     console.print(f"[green]MCP Response:[/green] {response}")
        # except Exception as e:
        #     console.print(f"[red]MCP Error:[/red] {e}")

    def repl(self):
        console.rule(f"[bold cyan]Ollama Turbo Chat CLI — Model: {self.model}")
        while True:
            try:
                prompt = session.prompt(f"({'Turbo' if self.use_turbo else 'Local'}:{self.model}): ",
                                        key_bindings=self.kb)
                if prompt.strip() == "":
                    continue
                if prompt.startswith("/models"):
                    parts = prompt.split()
                    if len(parts) == 2 and parts[1].isdigit():
                        self.pick_model(int(parts[1]))
                    else:
                        self.show_models()
                    continue
                if prompt.startswith("/help"):
                    self.show_help()
                    continue
                if prompt.startswith("/quit"):
                    break
                if prompt.startswith("/image"):
                    parts = prompt.split(maxsplit=1)
                    if len(parts) > 1:
                        self.images_to_send.append(parts[1])
                    continue
                if prompt.startswith("/mcp"):
                    parts = prompt.split(maxsplit=1)
                    command = parts[1] if len(parts) > 1 else None
                    self.run_mcp_tool(command)
                    continue

                # Add to history
                self.messages.append({"role": "user", "content": prompt})
                # Send to model
                if self.use_turbo and self.turbo_token:
                    stream = turbo_chat(self.turbo_token, self.messages)
                else:
                    stream = ollama_chat(self.model, self.messages, images=self.images_to_send or None)
                self.images_to_send = []
                out_text = ""
                with Live(console=console, refresh_per_second=4) as live:
                    for chunk in stream:
                        out_text += chunk
                        live.update(Text(out_text))
                self.messages.append({"role": "assistant", "content": out_text})
            except (EOFError, KeyboardInterrupt):
                console.print("\n[bold red]Bye![/bold red]")
                break

    def show_help(self):
        console.rule("[bold cyan]Commands and Shortcuts")
        console.print("""
/models [n]   — list models or switch to model number n
/help         — show this help
/quit         — exit
/image <path> — attach image for next message
/web <query>  — search the web and add to context
/mcp <cmd>    — call MCP tool

[Shortcuts]
Ctrl+I — insert /image
Ctrl+T — toggle Turbo
Ctrl+B — insert /web
Ctrl+M — insert /mcp
Ctrl+L — insert /models
""")

# ----------------------------------------------------------------------
def main():
    parser = argparse.ArgumentParser()
    parser.add_argument("--init", action="store_true")
    args = parser.parse_args()

    cli = ChatCLI()
    if args.init:
        cli.init_wizard()
    cli.repl()

if __name__ == "__main__":
    main()
