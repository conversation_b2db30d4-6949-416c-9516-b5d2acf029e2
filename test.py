#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys
import json
import yaml
import httpx
import argparse
import base64
import mimetypes
from pathlib import Path
from typing import List, Dict, Optional, Tuple
from prompt_toolkit.application import run_in_terminal

from rich.console import Console
from rich.table import Table
from rich.prompt import Prompt
from rich.panel import Panel
from rich.live import Live
from rich.text import Text
from rich import box

from prompt_toolkit import PromptSession
from prompt_toolkit.key_binding import KeyBindings

CONFIG_PATH = Path.home() / ".ollama_turbo_cli.yaml"
OLLAMA_URL = "http://127.0.0.1:11434"

console = Console()
session = PromptSession()

# ========= Config =========

def save_config(cfg: dict) -> None:
    with open(CONFIG_PATH, "w", encoding="utf-8") as f:
        yaml.safe_dump(cfg, f)

def load_config() -> dict:
    if CONFIG_PATH.exists():
        with open(CONFIG_PATH, "r", encoding="utf-8") as f:
            return yaml.safe_load(f) or {}
    return {}

# ========= Ollama helpers =========

def ollama_list_models() -> List[Dict]:
    try:
        r = httpx.get(f"{OLLAMA_URL}/api/tags", timeout=5)
        r.raise_for_status()
        return r.json().get("models", [])
    except Exception as e:
        console.print(f"[red]Failed to fetch models from Ollama: {e}[/red]")
        return []

def pick_smallest_model_name(models: List[Dict]) -> Optional[str]:
    if not models:
        return None
    smallest = sorted(models, key=lambda m: m.get("size", 0))[0]
    return smallest.get("name")

def model_is_vision(name: str) -> bool:
    # Heuristic: many vision models contain "vision" in the tag.
    # Adjust if your model naming differs.
    return "vision" in (name or "").lower()

def ollama_chat_stream(model: str, messages: List[Dict]) -> str:
    """Stream chat responses from local Ollama (correct image placement in messages)."""
    payload = {"model": model, "messages": messages, "stream": True}
    out_text = ""
    with httpx.stream("POST", f"{OLLAMA_URL}/api/chat", json=payload, timeout=None) as r:
        for line in r.iter_lines():
            if not line:
                continue
            try:
                data = json.loads(line)
            except Exception:
                continue
            # Newer Ollama streams chunks with 'message': {'content': '...'}
            if isinstance(data, dict) and "message" in data and isinstance(data["message"], dict):
                delta = data["message"].get("content", "")
                if delta:
                    yield delta
                    out_text += delta
            # Stop if done
            if data.get("done"):
                break
    return out_text

# ========= Web search =========

def web_search(query: str, num_related: int = 3) -> Tuple[str, str]:
    """
    DuckDuckGo Instant Answer API. Returns (title, body_text).
    """
    try:
        url = "https://api.duckduckgo.com/"
        params = {"q": query, "format": "json", "no_html": "1", "skip_disambig": "1"}
        response = httpx.get(url, params=params, timeout=10)
        response.raise_for_status()
        data = response.json()

        title = data.get("Heading") or f"Results for '{query}'"
        chunks = []

        if data.get("AbstractText"):
            chunks.append(f"Summary: {data['AbstractText']}")
        if data.get("Definition"):
            chunks.append(f"Definition: {data['Definition']}")

        related = data.get("RelatedTopics") or []
        added = 0
        for item in related:
            if isinstance(item, dict) and item.get("Text"):
                chunks.append(f"Related: {item['Text']}")
                added += 1
                if added >= num_related:
                    break

        body = "\n".join(chunks) if chunks else "No detailed results; try a more specific query."
        return (title, body)
    except Exception as e:
        return ("Web search failed", f"Error: {e}")

# ========= Turbo placeholder (still optional) =========

def turbo_available(token: Optional[str]) -> bool:
    return bool(token and len(token.strip()) > 10)

def turbo_chat_stream(token: str, messages: List[Dict]):
    # TODO: Replace with real Turbo endpoint call when you have it.
    user_msg = messages[-1].get("content") if messages else ""
    fake = f"[Turbo] (placeholder) I received: {user_msg}\n"
    for ch in fake:
        yield ch

# ========= CLI =========

class ChatCLI:
    def __init__(self):
        self.cfg = load_config()
        self.messages: List[Dict] = []
        self.model: Optional[str] = self.cfg.get("model")
        self.turbo_token: Optional[str] = self.cfg.get("turbo_token")
        self.use_turbo: bool = bool(self.cfg.get("use_turbo", False))
        self.kb = KeyBindings()
        self._bind_shortcuts()

        # init model fallback
        self._validate_and_fix_config()

    def _validate_and_fix_config(self) -> None:
        models = ollama_list_models()
        # ensure model exists & pick smallest if missing
        if not self.model:
            self.model = pick_smallest_model_name(models)
        else:
            names = [m["name"] for m in models]
            if self.model not in names:
                self.model = pick_smallest_model_name(models)
        self.cfg["model"] = self.model
        save_config(self.cfg)

    # ----- Shortcuts that DO actions -----
    def _bind_shortcuts(self):
    @self.kb.add("c-i")
    def _(event):
        def do():
            try:
                path = input("Image path: ").strip()
                if not path:
                    return
                caption = input("Caption (optional): ").strip()
                self.send_image_now(path, caption)
            except KeyboardInterrupt:
                pass
        run_in_terminal(do)

    @self.kb.add("c-b")
    def _(event):
        def do():
            try:
                q = input("Web query: ").strip()
                if q:
                    self.run_web_and_summarize(q)
            except KeyboardInterrupt:
                pass
        run_in_terminal(do)

    @self.kb.add("c-t")
    def _(event):
        # This doesn’t prompt, so it’s safe to run directly.
        self.use_turbo = not self.use_turbo
        mode = "Turbo" if self.use_turbo else "Local"
        console.print(f"[yellow]Switched to {mode} mode[/yellow]")

    @self.kb.add("c-l")
    def _(event):
        # Showing a table is also I/O; wrap in run_in_terminal for a clean redraw.
        run_in_terminal(self.show_models)


    # ----- Init wizard -----
    def init_wizard(self):
        console.rule("[bold cyan]Ollama Turbo Chat CLI — Initialization")
        console.print(f"Checking Ollama at {OLLAMA_URL} …")
        models = ollama_list_models()
        if models:
            console.print("[green]✔ Ollama detected and running[/green]")
            smallest = pick_smallest_model_name(models)
            self.model = smallest
            console.print(f"[cyan]Default model set to smallest available:[/cyan] {self.model}")
        else:
            console.print("[red]Could not detect Ollama or no models installed[/red]")
            self.model = None

        t = Prompt.ask("Ollama Turbo token (press Enter to skip)", default="")
        if t.strip():
            self.turbo_token = t.strip()
            # auto-enable turbo if token present
            self.use_turbo = True
            console.print("[green]Turbo configured[/green]")
        else:
            console.print("[yellow]Turbo not configured[/yellow]")

        self.cfg["model"] = self.model
        self.cfg["turbo_token"] = self.turbo_token
        self.cfg["use_turbo"] = self.use_turbo
        save_config(self.cfg)
        console.print("[bold green]Configuration saved successfully![/bold green]")

    # ----- Model listing/switching -----
    def show_models(self):
        models = ollama_list_models()
        if not models:
            console.print("[red]No models found or Ollama is not reachable.[/red]")
            return
        table = Table(title="Local Ollama Models", box=box.SIMPLE_HEAVY)
        table.add_column("#", justify="right")
        table.add_column("Name")
        table.add_column("Size (MB)", justify="right")
        for idx, m in enumerate(models, start=1):
            table.add_row(str(idx), m["name"], str(round(m.get("size", 0) / (1024*1024), 2)))
        console.print(table)
        # quick switch if user enters a number
        try:
            choice = Prompt.ask("Switch to # (Enter to skip)", default="")
            if choice.strip().isdigit():
                n = int(choice.strip())
                if 1 <= n <= len(models):
                    self.model = models[n-1]["name"]
                    self.cfg["model"] = self.model
                    save_config(self.cfg)
                    console.print(f"[green]Switched to model:[/green] {self.model}")
        except KeyboardInterrupt:
            return

    # ----- Image sending -----
    def _encode_image_b64(self, path: str) -> Optional[str]:
        p = Path(path).expanduser()
        if not p.exists():
            console.print(f"[red]Image not found: {path}[/red]")
            return None
        mime, _ = mimetypes.guess_type(str(p))
        if not mime or not mime.startswith("image/"):
            console.print(f"[red]Not an image file: {path}[/red]")
            return None
        with open(p, "rb") as f:
            return base64.b64encode(f.read()).decode("utf-8")

    def send_image_now(self, path: str, caption: str):
        if not self.model:
            console.print("[red]No model selected. Use /models to pick one.[/red]")
            return
        if not model_is_vision(self.model):
            console.print(f"[yellow]Current model '{self.model}' is not vision-capable.[/yellow]")
            # try to auto-pick a vision model if any
            vis = [m for m in ollama_list_models() if model_is_vision(m["name"])]
            if vis:
                choice = Prompt.ask(f"Switch to vision model '{vis[0]['name']}'? (y/N)", default="n")
                if choice.strip().lower() == "y":
                    self.model = vis[0]["name"]
                    self.cfg["model"] = self.model
                    save_config(self.cfg)
                else:
                    return
            else:
                console.print("[red]No vision model installed. Try pulling one like 'llama3.2-vision'.[/red]")
                return

        b64 = self._encode_image_b64(path)
        if not b64:
            return

        # Create a user message including images (correct placement)
        user_msg = {"role": "user", "content": caption or "Describe this image.", "images": [b64]}
        self.messages.append(user_msg)

        # Stream response
        self._stream_reply()

    # ----- Web → add to context + summarize -----
    def run_web_and_summarize(self, query: str):
        title, body = web_search(query)
        console.print(Panel.fit(f"🌐 [bold]Web Search[/bold] — {query}\n[dim]{title}[/dim]", border_style="cyan"))
        console.print(body if body else "No details.")

        # Add results to context as a system message
        self.messages.append({"role": "system", "content": f"Web results for '{query}':\n{body}"})
        # Ask model to summarize briefly
        self.messages.append({"role": "user", "content": "Summarize the web results above briefly and answer the query."})
        self._stream_reply()

    # ----- Core send/stream -----
    def _active_stream(self):
        # If turbo is enabled and token looks valid, try turbo; else local
        if self.use_turbo and turbo_available(self.turbo_token or ""):
            try:
                return "Turbo", turbo_chat_stream(self.turbo_token, self.messages)
            except Exception as e:
                console.print(f"[yellow]Turbo failed ({e}); falling back to local[/yellow]")
        # Local
        return f"Local:{self.model}", ollama_chat_stream(self.model, self.messages)

    def _stream_reply(self):
        mode, stream = self._active_stream()
        console.print(f"[dim]Using {mode}…[/dim]")
        out_text = ""
        with Live(console=console, refresh_per_second=8) as live:
            for chunk in stream:
                out_text += chunk
                live.update(Text(out_text))
        if out_text.strip():
            self.messages.append({"role": "assistant", "content": out_text})
        else:
            console.print("[yellow]No response received.[/yellow]")

    # ----- REPL -----
    def repl(self):
        ready = "Turbo" if (self.use_turbo and turbo_available(self.turbo_token or "")) else f"Local:{self.model}"
        console.rule(f"[bold cyan]Ollama Turbo Chat CLI — {ready}")
        console.print("[dim]Enter your message. /help for commands. Shortcuts: Ctrl+I (image), Ctrl+B (web), Ctrl+T (mode), Ctrl+L (models)[/dim]\n")

        while True:
            try:
                prompt = session.prompt(f"({'Turbo' if (self.use_turbo and turbo_available(self.turbo_token or '')) else 'Local'}:{self.model}): ",
                                        key_bindings=self.kb)
                if not prompt.strip():
                    continue

                if prompt.startswith("/help"):
                    self.show_help(); continue
                if prompt.startswith("/quit") or prompt.startswith("/exit"):
                    break
                if prompt.startswith("/clear"):
                    self.messages = []; console.print("[green]History cleared.[/green]"); continue
                if prompt.startswith("/status"):
                    self.show_status(); continue
                if prompt.startswith("/models"):
                    self.show_models(); continue
                if prompt.startswith("/image"):
                    # /image path "optional caption"
                    parts = prompt.split(maxsplit=2)
                    if len(parts) >= 2:
                        caption = parts[2].strip('"') if len(parts) == 3 else ""
                        self.send_image_now(parts[1], caption)
                    else:
                        console.print("[yellow]Usage: /image <path> \"optional caption\"[/yellow]")
                    continue
                if prompt.startswith("/web"):
                    q = prompt.split(" ", 1)[1].strip() if " " in prompt else ""
                    if not q:
                        q = session.prompt("Web query: ").strip()
                    if q:
                        self.run_web_and_summarize(q)
                    continue

                # Regular chat
                self.messages.append({"role": "user", "content": prompt})
                self._stream_reply()

            except (EOFError, KeyboardInterrupt):
                console.print("\n[red]Bye![/red]")
                break

    def show_status(self):
        table = Table(title="Current Status", box=box.SIMPLE_HEAVY)
        table.add_column("Setting", style="cyan")
        table.add_column("Value", style="green")
        table.add_row("Model", str(self.model))
        table.add_row("Mode", "Turbo" if (self.use_turbo and turbo_available(self.turbo_token or "")) else "Local")
        table.add_row("Turbo Token", "✓ Configured" if self.turbo_token else "✗ Not configured")
        table.add_row("Messages", str(len(self.messages)))
        table.add_row("Config File", str(CONFIG_PATH))
        console.print(table)

    def show_help(self):
        console.rule("[bold cyan]Commands & Shortcuts")
        console.print("""[bold yellow]Commands[/bold yellow]
/help                 Show this help
/status               Show current configuration/status
/models               List local models (then choose a number to switch)
/image <path> "cap"   Send an image (auto-switch to vision model if available)
/web <query>          Search web and auto-summarize into chat
/clear                Clear chat history
/quit (or /exit)      Quit

[bold yellow]Shortcuts[/bold yellow]
Ctrl+I  pick image & send     Ctrl+B  web search & summarize
Ctrl+T  toggle Turbo/Local    Ctrl+L  list models

[bold yellow]Tips[/bold yellow]
• The smallest local model is auto-selected by default.
• Image input attaches to the user message (correct for Ollama vision).
• Web results are added as system context, then summarized by the model.
""")

# ========= Main =========

def main():
    parser = argparse.ArgumentParser()
    parser.add_argument("--init", action="store_true", help="Run initialization wizard")
    args = parser.parse_args()

    cli = ChatCLI()
    if args.init:
        cli.init_wizard()
    cli.repl()

if __name__ == "__main__":
    main()
