
#!/usr/bin/env python3
import os
import sys
import json
import yaml
import httpx
import argparse
import signal
import threading
import base64
import mimetypes
from pathlib import Path
from typing import List, Dict, Optional
from rich.console import Console
from rich.table import Table
from rich.prompt import Prompt
from rich.live import Live
from rich.text import Text
from prompt_toolkit import PromptSession
from prompt_toolkit.key_binding import KeyBindings

CONFIG_PATH = Path.home() / ".ollama_turbo_cli.yaml"
OLLAMA_URL = "http://127.0.0.1:11434"

console = Console()
session = PromptSession()

# ----------------------------------------------------------------------
# Config management
# ----------------------------------------------------------------------
def save_config(cfg):
    with open(CONFIG_PATH, "w") as f:
        yaml.safe_dump(cfg, f)

def load_config():
    if CONFIG_PATH.exists():
        with open(CONFIG_PATH, "r") as f:
            return yaml.safe_load(f) or {}
    return {}

# ----------------------------------------------------------------------
# Ollama API helpers
# ----------------------------------------------------------------------
def ollama_list_models() -> List[Dict]:
    try:
        r = httpx.get(f"{OLLAMA_URL}/api/tags", timeout=5)
        r.raise_for_status()
        return r.json().get("models", [])
    except Exception as e:
        console.print(f"[red]Failed to fetch models from Ollama: {e}[/red]")
        return []

def ollama_chat(model: str, messages: List[Dict], images: Optional[List[str]] = None):
    """Stream chat responses from local Ollama."""
    payload = {"model": model, "messages": messages, "stream": True}
    if images:
        payload["images"] = images
    with httpx.stream("POST", f"{OLLAMA_URL}/api/chat", json=payload, timeout=None) as r:
        for line in r.iter_lines():
            if not line:
                continue
            try:
                data = json.loads(line)
                if "message" in data and "content" in data["message"]:
                    yield data["message"]["content"]
            except:
                continue

# ----------------------------------------------------------------------
# Web search functionality
# ----------------------------------------------------------------------
def web_search(query: str, num_results: int = 3) -> str:
    """Simple web search using DuckDuckGo Instant Answer API."""
    try:
        # Using DuckDuckGo Instant Answer API (free, no API key required)
        url = "https://api.duckduckgo.com/"
        params = {
            "q": query,
            "format": "json",
            "no_html": "1",
            "skip_disambig": "1"
        }

        response = httpx.get(url, params=params, timeout=10)
        response.raise_for_status()
        data = response.json()

        results = []

        # Add abstract if available
        if data.get("Abstract"):
            results.append(f"**Summary:** {data['Abstract']}")

        # Add definition if available
        if data.get("Definition"):
            results.append(f"**Definition:** {data['Definition']}")

        # Add related topics
        if data.get("RelatedTopics"):
            topics = data["RelatedTopics"][:num_results]
            for topic in topics:
                if isinstance(topic, dict) and topic.get("Text"):
                    results.append(f"**Related:** {topic['Text']}")

        if results:
            return "\n\n".join(results)
        else:
            return f"No detailed results found for '{query}'. You may want to try a more specific search term."

    except Exception as e:
        return f"Web search failed: {str(e)}"

# ----------------------------------------------------------------------
# MCP API functionality
# ----------------------------------------------------------------------
def call_mcp_api(api_key: str, command: str) -> str:
    """Call Smithery.ai MCP API."""
    try:
        # This is a placeholder implementation
        # Replace with actual Smithery.ai API endpoint and format
        url = "https://api.smithery.ai/v1/mcp"  # Replace with actual endpoint
        headers = {
            "Authorization": f"Bearer {api_key}",
            "Content-Type": "application/json"
        }
        payload = {
            "command": command,
            "format": "text"
        }

        response = httpx.post(url, json=payload, headers=headers, timeout=30)
        response.raise_for_status()

        result = response.json()
        return result.get("response", "No response from MCP API")

    except httpx.HTTPStatusError as e:
        if e.response.status_code == 401:
            return "Invalid API key. Please check your Smithery.ai API key."
        elif e.response.status_code == 429:
            return "Rate limit exceeded. Please try again later."
        else:
            return f"MCP API error ({e.response.status_code}): {e.response.text}"
    except Exception as e:
        return f"MCP API call failed: {str(e)}"

# ----------------------------------------------------------------------
# Turbo API placeholder
# ----------------------------------------------------------------------
def turbo_chat(token: str, messages: List[Dict]):
    """Placeholder for Turbo API - replace with actual implementation."""
    # You would replace this with actual Turbo API call
    # For now, simulate with a more realistic response
    user_message = messages[-1].get("content", "") if messages else ""
    yield f"[Turbo API Response] I received your message: '{user_message}'. "
    yield "This is a placeholder response. Please implement the actual Turbo API integration."

# ----------------------------------------------------------------------
# CLI Class
# ----------------------------------------------------------------------
class ChatCLI:
    def __init__(self):
        self.cfg = load_config()
        self.messages = []
        self.model = self.cfg.get("model", None)
        self.turbo_token = self.cfg.get("turbo_token")
        self.use_turbo = self.cfg.get("use_turbo", False)
        self.kb = KeyBindings()
        self._bind_shortcuts()
        self.images_to_send = []

    def init_wizard(self):
        console.rule("[bold cyan]Ollama Turbo Chat CLI — Initialization")
        # Auto detect Ollama
        console.print(f"Checking Ollama at {OLLAMA_URL} …")
        models = ollama_list_models()
        if models:
            console.print("[green]✔ Ollama detected and running[/green]")
            # pick smallest model
            smallest = sorted(models, key=lambda m: m.get("size", 0))[0]
            self.model = smallest["name"]
            console.print(f"[cyan]Default model set to smallest available:[/cyan] {self.model}")
        else:
            console.print("[red]Could not detect Ollama or no models installed[/red]")
            console.print("[yellow]Please install Ollama and pull at least one model:[/yellow]")
            console.print("  curl -fsSL https://ollama.ai/install.sh | sh")
            console.print("  ollama pull llama2  # or any other model")
            self.model = None

        # Turbo token
        t = Prompt.ask("Ollama Turbo token (press Enter to skip)", default="")
        if t.strip():
            self.turbo_token = t.strip()
            self.use_turbo = True
            console.print("[green]Turbo configured[/green]")
        else:
            console.print("Turbo not configured")

        # MCP key
        s = Prompt.ask("Smithery.ai API key (press Enter to skip)", default="")
        if s.strip():
            self.cfg["smithery_key"] = s.strip()
            console.print("[green]MCP configured[/green]")
        else:
            console.print("MCP not configured")

        self.cfg["model"] = self.model
        self.cfg["turbo_token"] = self.turbo_token
        self.cfg["use_turbo"] = self.use_turbo
        save_config(self.cfg)
        console.print("[bold green]Configuration saved successfully![/bold green]")

    def _bind_shortcuts(self):
        @self.kb.add("c-i")
        def _(event):
            event.app.current_buffer.insert_text("/image ")

        @self.kb.add("c-t")
        def _(_):
            self.use_turbo = not self.use_turbo
            mode = "Turbo" if self.use_turbo else "Local"
            console.print(f"[yellow]Switched to {mode} mode[/yellow]")

        @self.kb.add("c-b")
        def _(event):
            event.app.current_buffer.insert_text("/web ")

        @self.kb.add("c-m")
        def _(_):
            # Directly trigger MCP tool call instead of inserting text
            self.run_mcp_tool()

        @self.kb.add("c-l")
        def _(event):
            event.app.current_buffer.insert_text("/models")

    def show_models(self):
        models = ollama_list_models()
        if not models:
            return
        table = Table(title="Local Ollama Models")
        table.add_column("#", justify="right")
        table.add_column("Name")
        table.add_column("Size (MB)", justify="right")
        for idx, m in enumerate(models, start=1):
            table.add_row(str(idx), m["name"], str(round(m.get("size", 0) / (1024*1024), 2)))
        console.print(table)

    def pick_model(self, num: int):
        models = ollama_list_models()
        if 1 <= num <= len(models):
            self.model = models[num - 1]["name"]
            self.cfg["model"] = self.model
            save_config(self.cfg)
            console.print(f"[green]Switched to model:[/green] {self.model}")
        else:
            console.print("[red]Invalid model number[/red]")

    def encode_image(self, image_path: str) -> Optional[str]:
        """Encode image to base64 for Ollama vision models."""
        try:
            path = Path(image_path)
            if not path.exists():
                console.print(f"[red]Image file not found: {image_path}[/red]")
                return None

            # Check if it's an image file
            mime_type, _ = mimetypes.guess_type(str(path))
            if not mime_type or not mime_type.startswith('image/'):
                console.print(f"[red]File is not an image: {image_path}[/red]")
                return None

            with open(path, "rb") as f:
                image_data = f.read()
                encoded = base64.b64encode(image_data).decode('utf-8')
                console.print(f"[green]Image encoded:[/green] {path.name} ({len(image_data)} bytes)")
                return encoded

        except Exception as e:
            console.print(f"[red]Failed to encode image {image_path}: {e}[/red]")
            return None

    def run_web_search(self, query: str = None):
        """Run web search with optional query, or prompt for query if none provided."""
        if not query:
            try:
                query = session.prompt("Web search query: ")
                if not query.strip():
                    console.print("[yellow]No query entered[/yellow]")
                    return
            except (EOFError, KeyboardInterrupt):
                console.print("[yellow]Web search cancelled[/yellow]")
                return

        console.print(f"[cyan]Searching web for:[/cyan] {query}")
        with console.status("[bold green]Searching..."):
            results = web_search(query)

        console.print(f"[green]Search Results:[/green]\n{results}")

        # Add search results to conversation context
        context_msg = f"Web search results for '{query}':\n{results}"
        self.messages.append({"role": "system", "content": context_msg})

    def run_mcp_tool(self, command: str = None):
        """Run MCP tool with optional command, or prompt for command if none provided."""
        if not self.cfg.get("smithery_key"):
            console.print("[red]MCP not configured. Run with --init to set up Smithery.ai API key[/red]")
            return

        if not command:
            try:
                command = session.prompt("MCP command: ")
                if not command.strip():
                    console.print("[yellow]No command entered[/yellow]")
                    return
            except (EOFError, KeyboardInterrupt):
                console.print("[yellow]MCP command cancelled[/yellow]")
                return

        console.print(f"[cyan]MCP Tool Call:[/cyan] {command}")
        with console.status("[bold green]Calling MCP API..."):
            response = call_mcp_api(self.cfg["smithery_key"], command)

        console.print(f"[green]MCP Response:[/green] {response}")

        # Add MCP response to conversation context
        context_msg = f"MCP tool '{command}' response: {response}"
        self.messages.append({"role": "system", "content": context_msg})

    def show_status(self):
        """Show current configuration and status."""
        table = Table(title="Current Status")
        table.add_column("Setting", style="cyan")
        table.add_column("Value", style="green")

        table.add_row("Model", str(self.model))
        table.add_row("Mode", "Turbo" if self.use_turbo else "Local")
        table.add_row("Turbo Token", "✓ Configured" if self.turbo_token else "✗ Not configured")
        table.add_row("MCP API", "✓ Configured" if self.cfg.get("smithery_key") else "✗ Not configured")
        table.add_row("Messages in History", str(len(self.messages)))
        table.add_row("Images Queued", str(len(self.images_to_send)))
        table.add_row("Config File", str(CONFIG_PATH))

        console.print(table)

    def repl(self):
        console.rule(f"[bold cyan]Ollama Turbo Chat CLI — Model: {self.model}")
        while True:
            try:
                prompt = session.prompt(f"({'Turbo' if self.use_turbo else 'Local'}:{self.model}): ",
                                        key_bindings=self.kb)
                if prompt.strip() == "":
                    continue
                if prompt.startswith("/models"):
                    parts = prompt.split()
                    if len(parts) == 2 and parts[1].isdigit():
                        self.pick_model(int(parts[1]))
                    else:
                        self.show_models()
                    continue
                if prompt.startswith("/help"):
                    self.show_help()
                    continue
                if prompt.startswith("/quit") or prompt.startswith("/exit"):
                    break
                if prompt.startswith("/clear"):
                    self.messages = []
                    console.print("[green]Conversation history cleared[/green]")
                    continue
                if prompt.startswith("/status"):
                    self.show_status()
                    continue
                if prompt.startswith("/image"):
                    parts = prompt.split(maxsplit=1)
                    if len(parts) > 1:
                        encoded_image = self.encode_image(parts[1])
                        if encoded_image:
                            self.images_to_send.append(encoded_image)
                    else:
                        console.print("[yellow]Usage: /image <path_to_image>[/yellow]")
                    continue
                if prompt.startswith("/web"):
                    parts = prompt.split(maxsplit=1)
                    query = parts[1] if len(parts) > 1 else None
                    self.run_web_search(query)
                    continue
                if prompt.startswith("/mcp"):
                    parts = prompt.split(maxsplit=1)
                    command = parts[1] if len(parts) > 1 else None
                    self.run_mcp_tool(command)
                    continue

                # Add to history
                self.messages.append({"role": "user", "content": prompt})

                # Send to model
                try:
                    if self.use_turbo and self.turbo_token:
                        stream = turbo_chat(self.turbo_token, self.messages)
                    else:
                        if not self.model:
                            console.print("[red]No model selected. Use /models to select a model.[/red]")
                            continue
                        stream = ollama_chat(self.model, self.messages, images=self.images_to_send or None)

                    self.images_to_send = []  # Clear images after sending
                    out_text = ""

                    with Live(console=console, refresh_per_second=4) as live:
                        for chunk in stream:
                            out_text += chunk
                            live.update(Text(out_text))

                    if out_text.strip():
                        self.messages.append({"role": "assistant", "content": out_text})
                    else:
                        console.print("[yellow]No response received from model[/yellow]")

                except Exception as e:
                    console.print(f"[red]Error communicating with model: {e}[/red]")
                    # Remove the user message if there was an error
                    if self.messages and self.messages[-1]["role"] == "user":
                        self.messages.pop()
            except (EOFError, KeyboardInterrupt):
                console.print("\n[bold red]Bye![/bold red]")
                break

    def show_help(self):
        console.rule("[bold cyan]Commands and Shortcuts")
        console.print("""
[bold yellow]Commands:[/bold yellow]
/models [n]   — list models or switch to model number n
/help         — show this help
/quit, /exit  — exit the application
/clear        — clear conversation history
/status       — show current configuration and status
/image <path> — attach image for next message (vision models)
/web <query>  — search the web and add results to context
/mcp <cmd>    — call MCP tool (requires Smithery.ai API key)

[bold yellow]Shortcuts:[/bold yellow]
Ctrl+I — insert /image command
Ctrl+T — toggle between Turbo and Local mode
Ctrl+B — insert /web command
Ctrl+M — directly trigger MCP tool (prompts for command)
Ctrl+L — insert /models command

[bold yellow]Tips:[/bold yellow]
• Use --init flag to run initial setup wizard
• Images are automatically encoded for vision-capable models
• Web search results are added to conversation context
• MCP tools require configuration during setup
""")

# ----------------------------------------------------------------------
# Utility functions
# ----------------------------------------------------------------------
def check_dependencies():
    """Check if required dependencies are available."""
    missing = []
    try:
        import yaml
    except ImportError:
        missing.append("pyyaml")

    try:
        import httpx
    except ImportError:
        missing.append("httpx")

    try:
        from rich.console import Console
    except ImportError:
        missing.append("rich")

    try:
        from prompt_toolkit import PromptSession
    except ImportError:
        missing.append("prompt_toolkit")

    if missing:
        print(f"Missing required dependencies: {', '.join(missing)}")
        print("Install them with: pip install " + " ".join(missing))
        return False
    return True

def main():
    """Main entry point."""
    if not check_dependencies():
        sys.exit(1)

    parser = argparse.ArgumentParser(
        description="Ollama Turbo Chat CLI - A feature-rich chat interface",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  python test.py --init    # Run initial setup wizard
  python test.py           # Start chat interface
        """
    )
    parser.add_argument("--init", action="store_true",
                       help="Run initialization wizard to configure the CLI")
    parser.add_argument("--version", action="version", version="Ollama Turbo CLI v1.0")

    args = parser.parse_args()

    try:
        cli = ChatCLI()
        if args.init:
            cli.init_wizard()
        else:
            # Check if configuration exists
            if not cli.cfg and not Path(CONFIG_PATH).exists():
                console.print("[yellow]No configuration found. Running setup wizard...[/yellow]")
                cli.init_wizard()

        cli.repl()
    except KeyboardInterrupt:
        console.print("\n[bold red]Interrupted by user[/bold red]")
        sys.exit(0)
    except Exception as e:
        console.print(f"[bold red]Fatal error: {e}[/bold red]")
        sys.exit(1)

if __name__ == "__main__":
    main()
