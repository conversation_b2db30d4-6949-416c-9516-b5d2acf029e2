#!/usr/bin/env python3
"""
Simple test script for the Ollama Turbo Chat CLI
"""

import sys
import tempfile
from pathlib import Path

# Add the current directory to the path so we can import our module
sys.path.insert(0, str(Path(__file__).parent))

from test import ChatCL<PERSON>, web_search, check_dependencies

def test_dependencies():
    """Test that all required dependencies are available."""
    print("Testing dependencies...")
    assert check_dependencies(), "Dependencies check failed"
    print("✓ All dependencies available")

def test_web_search():
    """Test web search functionality."""
    print("Testing web search...")
    result = web_search("python programming", num_results=1)
    assert isinstance(result, str), "Web search should return a string"
    assert len(result) > 0, "Web search should return non-empty result"
    print("✓ Web search working")

def test_cli_initialization():
    """Test CLI initialization."""
    print("Testing CLI initialization...")
    cli = ChatCLI()
    assert hasattr(cli, 'cfg'), "CLI should have config attribute"
    assert hasattr(cli, 'messages'), "CLI should have messages attribute"
    assert hasattr(cli, 'kb'), "CLI should have key bindings"
    print("✓ CLI initialization working")

def test_image_encoding():
    """Test image encoding functionality."""
    print("Testing image encoding...")
    cli = ChatCLI()
    
    # Test with non-existent file
    result = cli.encode_image("nonexistent.jpg")
    assert result is None, "Should return None for non-existent file"
    
    # Create a temporary text file (not an image)
    with tempfile.NamedTemporaryFile(suffix=".txt", delete=False) as f:
        f.write(b"test content")
        temp_path = f.name
    
    try:
        result = cli.encode_image(temp_path)
        assert result is None, "Should return None for non-image file"
    finally:
        Path(temp_path).unlink()
    
    print("✓ Image encoding validation working")

def main():
    """Run all tests."""
    print("Running Ollama Turbo Chat CLI tests...\n")
    
    try:
        test_dependencies()
        test_web_search()
        test_cli_initialization()
        test_image_encoding()
        
        print("\n🎉 All tests passed!")
        return 0
        
    except Exception as e:
        print(f"\n❌ Test failed: {e}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
