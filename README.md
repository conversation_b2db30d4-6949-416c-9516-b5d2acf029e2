# Ollama Turbo Chat CLI

A feature-rich command-line interface for chatting with Ollama models, with support for Turbo API, web search, MCP tools, and vision capabilities.

## Features

- 🤖 **Local Ollama Integration** - Chat with locally installed Ollama models
- ⚡ **Turbo API Support** - Switch between local and cloud-based models
- 🔍 **Web Search** - Integrated web search using DuckDuckGo API
- 🛠️ **MCP Tools** - Model Context Protocol integration via Smithery.ai
- 👁️ **Vision Support** - Send images to vision-capable models
- ⌨️ **Keyboard Shortcuts** - Quick access to common commands
- 💾 **Persistent Configuration** - Saves your settings automatically
- 🎨 **Rich Interface** - Beautiful terminal UI with colors and formatting

## Installation

1. **Install Python dependencies:**
   ```bash
   pip install -r requirements.txt
   ```

2. **Install Ollama (if not already installed):**
   ```bash
   curl -fsSL https://ollama.ai/install.sh | sh
   ```

3. **Pull at least one model:**
   ```bash
   ollama pull llama2
   # or any other model you prefer
   ```

## Quick Start

1. **Run the setup wizard:**
   ```bash
   python test.py --init
   ```

2. **Start chatting:**
   ```bash
   python test.py
   ```

## Commands

| Command | Description |
|---------|-------------|
| `/models [n]` | List available models or switch to model number n |
| `/help` | Show help information |
| `/quit`, `/exit` | Exit the application |
| `/clear` | Clear conversation history |
| `/status` | Show current configuration and status |
| `/image <path>` | Attach image for next message (vision models) |
| `/web <query>` | Search the web and add results to context |
| `/mcp <cmd>` | Call MCP tool (requires Smithery.ai API key) |

## Keyboard Shortcuts

| Shortcut | Action |
|----------|--------|
| `Ctrl+I` | Insert `/image` command |
| `Ctrl+T` | Toggle between Turbo and Local mode |
| `Ctrl+B` | Insert `/web` command |
| `Ctrl+M` | Directly trigger MCP tool (prompts for command) |
| `Ctrl+L` | Insert `/models` command |

## Configuration

The CLI stores configuration in `~/.ollama_turbo_cli.yaml`. You can edit this file manually or use the `--init` flag to reconfigure.

### Configuration Options

- **model**: Default Ollama model to use
- **turbo_token**: API token for Turbo service
- **use_turbo**: Whether to use Turbo by default
- **smithery_key**: API key for Smithery.ai MCP integration

## Usage Examples

### Basic Chat
```bash
python test.py
> Hello, how are you?
```

### Image Analysis (Vision Models)
```bash
> /image /path/to/image.jpg
> What do you see in this image?
```

### Web Search Integration
```bash
> /web latest news about AI
> Based on the search results, what are the key developments?
```

### Model Switching
```bash
> /models
> /models 2  # Switch to model #2
```

## Troubleshooting

### Common Issues

1. **"No models available"**
   - Make sure Ollama is running: `ollama serve`
   - Pull a model: `ollama pull llama2`

2. **"Failed to connect to Ollama"**
   - Check if Ollama is running on the default port (11434)
   - Verify Ollama installation

3. **Missing dependencies**
   - Install requirements: `pip install -r requirements.txt`

### Getting Help

Run `python test.py` and type `/help` for in-app help, or use `python test.py --help` for command-line options.

## Development

The application is structured as follows:

- **Configuration Management**: YAML-based config in user's home directory
- **Ollama Integration**: Direct API calls to local Ollama instance
- **Web Search**: DuckDuckGo Instant Answer API (no API key required)
- **MCP Integration**: Smithery.ai API for Model Context Protocol tools
- **UI**: Rich terminal interface with prompt_toolkit for input handling

## License

This project is open source. Feel free to modify and distribute as needed.
