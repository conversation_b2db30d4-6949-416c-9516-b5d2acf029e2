# Ollama Turbo Chat CLI Requirements
# Install with: pip install -r requirements.txt

# Core dependencies
httpx>=0.24.0          # HTTP client for API calls
pyyaml>=6.0            # YAML configuration file handling
rich>=13.0.0           # Rich terminal output and formatting
prompt_toolkit>=3.0.0  # Advanced prompt and input handling

# Optional dependencies (uncomment if needed)
# pillow>=9.0.0        # Image processing (if you want to add image manipulation)
# requests>=2.28.0     # Alternative HTTP client (if you prefer requests over httpx)
