#!/usr/bin/env python3
"""
Demo script showing the Ollama Turbo Chat CLI functionality
"""

import sys
from pathlib import Path

# Add the current directory to the path so we can import our module
sys.path.insert(0, str(Path(__file__).parent))

from test import web_search
from rich.console import Console

console = Console()

def demo_web_search():
    """Demonstrate web search functionality."""
    console.rule("[bold cyan]Web Search Demo")
    
    queries = [
        "Python programming basics",
        "Machine learning trends 2024",
        "Ollama AI models"
    ]
    
    for query in queries:
        console.print(f"\n[yellow]Searching for:[/yellow] {query}")
        result = web_search(query, num_results=2)
        console.print(f"[green]Results:[/green]\n{result[:200]}...")



def main():
    """Run the demo."""
    console.print("[bold green]Ollama Turbo Chat CLI - Feature Demo[/bold green]\n")
    
    console.print("This demo shows the key features of the CLI without requiring full setup.\n")
    
    try:
        demo_web_search()

        console.print("\n[bold green]Demo completed![/bold green]")
        console.print("\nTo try the full CLI:")
        console.print("1. Run: [cyan]python3 test.py --init[/cyan]")
        console.print("2. Then: [cyan]python3 test.py[/cyan]")

    except Exception as e:
        console.print(f"[red]Demo error: {e}[/red]")
        return 1
    
    return 0

if __name__ == "__main__":
    sys.exit(main())
